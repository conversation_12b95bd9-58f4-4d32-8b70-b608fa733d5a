/*
 * @Author: root <EMAIL>
 * @Date: 2025-07-29 13:42:03
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-05 15:29:38
 * @FilePath: \key-focus-front\packages\key-hmes-front\src\routes\IncomingInspectionDashboard\components\ECharts.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Description: 自定义ECharts组件，支持强制撑满宽度
 * @Author:
 * @Date: 2025-07-29 13:43:39
 * @LastEditTime: 2025-07-29 13:43:39
 */
import React, { PureComponent, createRef } from 'react';
import * as echarts from 'echarts';

export default class ECharts extends PureComponent {
  constructor(props) {
    super(props);
    this.echart_react = createRef();
    this.echart = null;
  }

  componentDidMount() {
    this.initChart();
    this.bindEvents();
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  }

  componentDidUpdate(prevProps) {
    if (this.echart && prevProps.option !== this.props.option) {
      const { notMerge = false, lazyUpdate = false, forceFullWidth = false } = this.props;

      let option = this.props.option;

      // 如果启用了强制撑满宽度，修改相关配置
      if (forceFullWidth && option) {
        option = this.adjustOptionForFullWidth(option);
      }

      this.echart.setOption(option, notMerge, lazyUpdate);
      // 强制resize确保图表正确显示
      setTimeout(() => {
        if (this.echart) {
          this.echart.resize();
        }
      }, 100);
    }
  }

  componentWillUnmount() {
    if (this.echart) {
      this.echart.dispose();
      this.echart = null;
    }
    window.removeEventListener('resize', this.handleResize);
  }

  initChart = () => {
    if (this.echart_react.current) {
      this.echart = echarts.init(this.echart_react.current, null, {
        renderer: 'canvas',
        width: 'auto',
        height: 'auto',
      });

      if (this.props.option) {
        const { notMerge = false, lazyUpdate = false, forceFullWidth = false } = this.props;

        let option = this.props.option;

        // 如果启用了强制撑满宽度，修改相关配置
        if (forceFullWidth) {
          option = this.adjustOptionForFullWidth(option);
        }

        this.echart.setOption(option, notMerge, lazyUpdate);
      }
    }
  };

  // 调整配置以强制撑满宽度
  adjustOptionForFullWidth = (option) => {
    const adjustedOption = { ...option };

    // 调整grid配置
    if (adjustedOption.grid) {
      adjustedOption.grid = {
        ...adjustedOption.grid,
        left: '3%',
        right: '3%',
        containLabel: true,
      };
    }

    // 调整xAxis配置
    if (adjustedOption.xAxis && Array.isArray(adjustedOption.xAxis)) {
      adjustedOption.xAxis = adjustedOption.xAxis.map(axis => ({
        ...axis,
        boundaryGap: false, // 强制从边界开始
      }));
    }

    // 调整series配置
    if (adjustedOption.series && Array.isArray(adjustedOption.series)) {
      adjustedOption.series = adjustedOption.series.map(series => {
        if (series.type === 'bar') {
          return {
            ...series,
            barWidth: '50%',
            barMaxWidth: 100,
          };
        }
        return series;
      });
    }

    return adjustedOption;
  };

  bindEvents = () => {
    if (this.echart && this.props.onEvents) {
      Object.keys(this.props.onEvents).forEach(eventName => {
        this.echart.on(eventName, this.props.onEvents[eventName]);
      });
    }
  };

  handleResize = () => {
    if (this.echart) {
      // 延迟执行resize，确保容器尺寸已经更新
      setTimeout(() => {
        if (this.echart) {
          this.echart.resize();
        }
      }, 100);
    }
  };

  // 提供给外部调用的resize方法
  resize = () => {
    this.handleResize();
  };

  // 获取echarts实例
  getEchartsInstance = () => {
    return this.echart;
  };

  render() {
    const { style = {} } = this.props;
    const defaultStyle = {
      width: '100%',
      height: '100%',
      minHeight: '210px',
      ...style,
    };

    return (
      <div 
        ref={this.echart_react} 
        style={defaultStyle}
      />
    );
  }
}
