/*
 * @Description: 全屏效果工具函数
 * @version: 0.1.0
 * @Author: 参考OperationPlatform实现
 * @Date: 2025-08-05
 * @Copyright: Copyright (c) 2019 Hand
 */

export function IEVersion() {
  // eslint-disable-next-line
  const userAgent = navigator.userAgent; // 取得浏览器的userAgent字符串
  const isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1; // 判断是否IE<11浏览器
  const isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1;
  return isIE || isIE11;
}

// 全屏
export function launchFullscreen(element) {
  console.log('进入全屏模式:', element);
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullScreen();
  }
}

// 退出全屏
export function exitFullscreen() {
  console.log('退出全屏模式');
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen();
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  }
}

// 检查是否支持全屏
export const fullScreenEnabled =
  document.fullScreenEnabled ||
  document.webkitFullScreenEnabled ||
  document.mozFullScreenEnabled ||
  document.msFullScreenEnabled;

// 检查当前是否处于全屏状态
export function isFullscreen() {
  return !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  );
}
