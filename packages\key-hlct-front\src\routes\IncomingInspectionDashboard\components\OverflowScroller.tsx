import React, { useEffect, useState, useRef } from 'react';
import styles from '../index.module.less';

interface OverflowScrollerProps {
  children: React.ReactNode;
  className?: string;
}

const OverflowScroller = React.memo(({ children, className = '' }: OverflowScrollerProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [style, setStyle] = useState<any>({});

  useEffect(() => {
    const container = containerRef.current;

    if (container) {
      const checkOverflow = () => {
        const isCurrentlyOverflowing = container.scrollWidth > container.clientWidth;
        setIsOverflowing(isCurrentlyOverflowing);

        if (isCurrentlyOverflowing) {
          const overflowAmount = container.scrollWidth - container.clientWidth;
          // 往复轮播需要更长的持续时间，因为要来回滚动
          const duration = Math.max(8, overflowAmount / 10); // 10px/s, min 8s
          setStyle({
            '--scroll-amount': `-${overflowAmount + 16}px`, // Add some padding
            '--scroll-duration': `${duration}s`,
          });
        }
      };

      // 使用防抖来减少频繁的检查
      let timeoutId: NodeJS.Timeout;
      const debouncedCheckOverflow = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(checkOverflow, 100);
      };

      const resizeObserver = new ResizeObserver(debouncedCheckOverflow);
      resizeObserver.observe(container);

      checkOverflow();
      document.fonts.ready.then(() => {
        checkOverflow();
      });

      return () => {
        clearTimeout(timeoutId);
        resizeObserver.disconnect();
      };
    }
  }, [children]);

  return (
    <div ref={containerRef} className={`${styles.tableCell} ${className}`}>
      <span
        className={isOverflowing ? styles.scrollingText : ''}
        style={style as React.CSSProperties}
      >
        {children}
      </span>
    </div>
  );
});

OverflowScroller.displayName = 'OverflowScroller';

export default OverflowScroller;
