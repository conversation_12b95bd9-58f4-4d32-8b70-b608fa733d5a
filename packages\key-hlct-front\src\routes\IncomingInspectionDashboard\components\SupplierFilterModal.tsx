/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-08-01 18:09:32
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-04 12:22:14
 * @FilePath: \inja-qms-front\packages\key-hlct-front\src\routes\IncomingInspectionDashboard\components\SupplierFilterModal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import styles from '../index.module.less';
import SupplierFilterContent from './SupplierFilterContent';

interface SupplierFilterModalProps {
  isOpen: boolean;
  dataSet: any;
  selectedRecord: any;
  onSelect: (record: any) => void;
  onConfirm: () => void;
  onCancel: () => void;
  supplierPageData: any[]; // 添加供应商数据参数
  onQuery: (supplierCode: string, supplierName: string) => void; // 添加查询回调
}

const SupplierFilterModal: React.FC<SupplierFilterModalProps> = ({
  isOpen,
  dataSet,
  selectedRecord,
  onSelect,
  onConfirm,
  onCancel,
  supplierPageData,
  onQuery,
}) => {
  if (!isOpen) return null;

  return (
    <div
      className={styles.modalOverlay}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
      }}
    >
      <div className={styles.materialFilterModalContainer}>
        <div className={styles.materialFilterModalTitle}>供应商筛选</div>
        <div className={styles.materialFilterModalBody}>
          <SupplierFilterContent
            dataSet={dataSet}
            onSelect={onSelect}
            selectedRecord={selectedRecord}
            supplierPageData={supplierPageData}
            onQuery={onQuery}
          />
        </div>
        <div className={styles.materialFilterModalFooter}>
          <button
            type="button"
            className={`${styles.filterButton}`}
            onClick={onCancel}
          >
            取消
          </button>
          <button
            type="button"
            className={`${styles.filterButton}`}
            onClick={onConfirm}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  );
};

export default SupplierFilterModal;
