import React, { useState } from 'react';
import styles from '../index.module.less';

interface SupplierFilterContentProps {
  dataSet: any;
  onSelect: (record: any) => void;
  selectedRecord: any;
  supplierPageData: any[]; // 从主组件传入的供应商数据
  onQuery: (supplierCode: string, supplierName: string) => void; // 新增查询回调
}

const SupplierFilterContent = ({
  dataSet,
  onSelect,
  selectedRecord,
  supplierPageData,
  onQuery,
}: SupplierFilterContentProps) => {
  const [supplierCodeFilter, setSupplierCodeFilter] = useState('');
  const [supplierNameFilter, setSupplierNameFilter] = useState('');

  // 使用从主组件传入的真实数据
  const allRecords = supplierPageData.map(item => ({
    code: item.supplierCode,
    description: item.supplier,
    supplierId: item.supplierId,
    ...item, // 保留原始数据
  }));

  // 显示所有数据，不进行前端筛选
  const filteredRecords = allRecords;

  const handleClearFilters = () => {
    setSupplierCodeFilter('');
    setSupplierNameFilter('');
  };

  const selectedCode = selectedRecord ? selectedRecord.code : null;

  return (
    <div className={styles.materialFilterList}>
      {/* 筛选条件区域 */}
      <div className={styles.materialFilterControls}>
        <div className={styles.filterRow}>
          <div className={styles.filterItem}>
            <label className={styles.filterLabel}>供应商编码:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="请输入供应商编码"
              value={supplierCodeFilter}
              onChange={e => setSupplierCodeFilter(e.target.value)}
            />
          </div>
          <div className={styles.filterItem}>
            <label className={styles.filterLabel}>供应商名称:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="请输入供应商名称"
              value={supplierNameFilter}
              onChange={e => setSupplierNameFilter(e.target.value)}
            />
          </div>
          <div className={styles.filterButtonBox}>
            <button type="button" className={styles.filterButton} onClick={handleClearFilters}>
              重置
            </button>
            <button
              type="button"
              className={styles.filterButton}
              onClick={() => {
                onQuery(supplierCodeFilter, supplierNameFilter);
              }}
            >
              查询
            </button>
          </div>
        </div>
      </div>

      {/* 列表头部 */}
      <div className={styles.materialFilterListHeader}>
        <span>供应商编码</span>
        <span>供应商描述</span>
      </div>

      {/* 列表内容 */}
      <div className={styles.materialFilterListBody}>
        {filteredRecords.length > 0 ? (
          filteredRecords.map((record: any) => {
            const recordCode = record.code;
            const recordDescription = record.description;

            return (
              <div
                key={recordCode}
                className={`${styles.materialFilterListRow} ${
                  selectedCode === recordCode ? styles.selected : ''
                }`}
                onClick={() => onSelect(record)}
              >
                <span>{recordCode}</span>
                <span>{recordDescription}</span>
              </div>
            );
          })
        ) : (
          <div className={styles.noDataRow}>
            <span>暂无匹配的供应商数据</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupplierFilterContent;
