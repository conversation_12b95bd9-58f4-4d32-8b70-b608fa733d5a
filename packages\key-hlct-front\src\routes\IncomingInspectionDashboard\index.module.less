/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-15 14:17:05
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-06 09:52:11
 * @FilePath: \institute-front\packages\key-hmes-front\src\routes\workshop\InspectionMonitorBoard\index.module.less
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
.bgBac {
  background-image: url('../../assets/IncomingInspectionDashboard/bac.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top center;
}

.bgTimeBac {
  background-image: url('../../assets/IncomingInspectionDashboard/time_bac.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.bgHeaderTitle {
  background-image: url('../../assets/IncomingInspectionDashboard/header_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
.bgPieBac {
  background-image: url('../../assets/IncomingInspectionDashboard/pie_bac.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.bgTableTitle {
  background-image: url('../../assets/IncomingInspectionDashboard/table_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.bgBottomContentNum {
  background-image: url('../../assets/IncomingInspectionDashboard/bottom_content_num.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.bgModal {
  background-image: url('../../assets/IncomingInspectionDashboard/modal.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.datePickerWrapper {
  width: 18rem;
  height: 3rem;
  position: relative;
}

.headerTitle {
  height: 3rem;
  width: 40.4rem;
}

.pieChartBackground {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

:global(html) {
  font-size: calc(100vw / 192);

  @media screen and (max-width: 1600px) and (min-width: 1400px) {
    font-size: calc(100vw / 185);
  }

  @media screen and (max-width: 1400px) and (min-width: 1200px) {
    font-size: calc(100vw / 178);
  }

  @media screen and (max-width: 1200px) {
    font-size: calc(100vw / 170);
  }

  @media screen and (min-width: 2560px) {
    font-size: calc(100vw / 200);
  }
}

:global(*) {
  @media screen and (max-width: 1600px) {
    box-sizing: border-box !important;
    max-width: 100% !important;
  }
}
.boardContainer {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 1.5rem 3rem;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  max-width: 100vw; // 防止横向溢出

  // 确保在不同缩放比例下的稳定性
  @media screen and (max-width: 1600px) {
    padding: 0 0.8rem 2.5rem; // 125%缩放时大幅减少左右内边距
  }

  @media screen and (max-width: 1400px) {
    padding: 0 0.5rem 2rem; // 150%缩放时进一步减少内边距
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  height: 5rem;
  padding: 1.5rem 3rem;
  box-sizing: border-box;
  position: relative;
}

.headerLeft,
.headerRight {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  flex: 1;
}

.headerRight {
  justify-content: flex-end;
  margin-right: 4%;
}

.fullscreenButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.2rem;
  height: 3.2rem;
  background: transparent;
  border: none;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 1rem;

  &:hover {
    color: #4a90e2;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.9);
  }

  :global(.icon) {
    font-size: 2.4rem;
  }
}

.datePickerWrapper {
  width: 18rem;
  height: 3rem;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 1rem;
  flex-shrink: 0;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 86% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-shadow: none !important;
      outline: none !important;
      position: relative !important;
    }

    .c7n-pro-calendar-picker {
      background-color: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 90% !important;
      color: #ffffff !important;
      font-size: 1.4rem !important;
      padding-left: 5rem !important;
      display: flex !important;
      align-items: center !important;
      line-height: 3rem !important;

      input {
        background: transparent !important;
        border: none !important;
        display: flex !important;
        align-items: center !important;
        color: #b9d4ff !important;
        font-size: 1.4rem !important;
        width: 100% !important;
        height: 100% !important;
        text-align: left !important;
        box-shadow: none !important;
        outline: none !important;
        line-height: 3rem !important;

        &::placeholder {
          color: #b9d4ff !important;
          opacity: 1 !important;
          font-size: 1.4rem !important;
        }

        &:focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
          color: #b9d4ff !important;
        }
      }
    }

    .c7n-pro-custom-date-picker {
      font-size: 1.4rem !important;
      background: transparent !important;
      height: 3rem;
      width: 14rem;
      border: 0;
      padding-left: 4rem;

      input {
        background: transparent !important;
        border: none !important;
        display: flex !important;
        align-items: center !important;
        color: #b9d4ff !important;
        font-size: 1.4rem !important;
        width: 100% !important;
        height: 100% !important;
        text-align: left !important;
        box-shadow: none !important;
        outline: none !important;
        line-height: 3rem !important;

        &::placeholder {
          color: #b9d4ff !important;
          opacity: 1 !important;
          font-size: 1.4rem !important;
        }

        &:focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
          color: #b9d4ff !important;
        }
      }
    }
    .c7n-pro-calendar-picker-inner {
      background: transparent !important;
      width: 100% !important;
      height: 100% !important;
    }

    .c7n-pro-calendar-picker-suffix {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 1.05rem 2.03rem;
      .icon {
        color: #b9d4ff !important;
        font-size: 1.4rem !important;
      }
    }

    .icon-date_range {
      color: #c3e1e9 !important;
      font-size: 1.4rem !important;
    }
  }
}

:global(.custom-date-picker) {
  .c7n-pro-calendar-picker-wrapper {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
  }

  .c7n-pro-calendar-cell-inner {
    width: 100% !important;
    color: #fff !important;
    height: 100% !important;
    font-size: 1.2rem;
  }

  .c7n-pro-calendar-picker {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
  }
}

.dateLabel {
  font-size: 1.4rem;
  color: #b9d4ff;
}

.titleGroup {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.title {
  background-image: url('../../assets/IncomingInspectionDashboard/header_title.png');
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 2.6rem;
  width: 36rem;
}

.enTitle {
  font-size: 1.3rem;
  letter-spacing: 0.2rem;
  color: #fff;
  opacity: 0.7;
  padding-top: 0.4rem;
}

.headerCenter {
  position: absolute;
  top: 3.6rem;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 10;
}

.timeWrapper {
  display: flex;
  flex-direction: row;
  align-items: end;
  gap: 1.5rem;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 1rem 0;
}
.time {
  font-size: 2.2rem;
  color: #fff;
}
.date {
  font-size: 1.6rem;
  color: #fff;
}
.week {
  font-size: 1.6rem;
  color: #fff;
}

.mainContent {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 0;
  box-sizing: border-box;
  margin-top: 1.4rem;
  padding: 0 2rem 2rem;
  max-width: 100%;
  overflow-x: hidden;

  @media screen and (max-width: 1600px) {
    gap: 1.5rem;
    margin-top: 1rem;
    padding: 0 0.8rem 1.5rem;
  }

  @media screen and (max-width: 1400px) {
    gap: 1.2rem; // 150%缩放时进一步减少间距
    margin-top: 0.8rem;
    padding: 0 0.5rem 1.2rem; // 进一步减少左右内边距
  }
}

.topRow {
  display: flex;
  flex: 1.4;
  min-height: 0;
  gap: 2rem;

  > .panel {
    flex: 1;
    min-width: 0;
    min-height: 0; // 确保panel可以收缩
    overflow: hidden; // 防止内容溢出
  }

  > .panel:nth-child(2) {
    margin: 2.5rem 3rem 0 6rem;

    .panelHeader {
      padding: 1.8rem 0px 0px 2.2rem;
    }

    // 125%缩放时的调整 - 大幅减少左右边距防止溢出
    @media screen and (max-width: 1600px) {
      margin: 2rem 1.5rem 0 3rem; // 大幅减少左右边距

      .panelHeader {
        padding: 1.5rem 0px 0px 1.5rem;
      }
    }

    // 150%缩放时的调整
    @media screen and (max-width: 1400px) {
      margin: 1.8rem 1rem 0 2rem; // 进一步减少左右边距

      .panelHeader {
        padding: 1.2rem 0px 0px 1.2rem;
      }
    }
  }

  // 整体间距调整
  @media screen and (max-width: 1600px) {
    gap: 1.5rem;
    max-height: 55vh; // 125%缩放时减少最大高度，但保持更大空间
  }

  @media screen and (max-width: 1400px) {
    gap: 1.2rem;
    max-height: 50vh; // 150%缩放时进一步减少最大高度，但保持更大空间
  }
}

.bottomRow {
  display: grid;
  grid-template-columns: 4.5fr 4.7fr;
  gap: 4rem;
  flex: 1;
  min-height: 0;
  max-width: 100%; // 防止横向溢出
  overflow-x: hidden; // 隐藏横向滚动条

  // 针对不同缩放比例的响应式调整
  @media screen and (max-width: 1600px) {
    gap: 1.5rem; // 125%缩放时大幅减少间距防止溢出
    grid-template-columns: 1fr 1fr; // 使用相等的列宽
  }

  @media screen and (max-width: 1400px) {
    gap: 1rem; // 150%缩放时进一步减少间距
    grid-template-columns: 1fr 1fr; // 保持相等的列宽
  }
}

.panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  background: transparent;
  padding-top: 0;
}

.panelHeader {
  display: flex;
  align-items: center;
  height: 4.4rem;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-shrink: 0;
  padding: 1.3rem 0 0 4rem;

  // 针对不同缩放比例的响应式调整
  @media screen and (max-width: 1600px) {
    height: 4rem; // 125%缩放时减少高度
    padding: 1.1rem 0 0 3.5rem;
    margin-bottom: 0.8rem;
  }

  @media screen and (max-width: 1400px) {
    height: 3.6rem; // 150%缩放时进一步减少高度
    padding: 1rem 0 0 3rem;
    margin-bottom: 0.6rem;
  }
}

// .bottomRow .panel:nth-child(1) .panelHeader {
//   padding: 2.4rem 0px 0px 5rem !important; // 减少左边距，使标题位置更合适

//   @media screen and (max-width: 1600px) {
//     padding: 2.4rem 0px 0px 5rem !important; // 125%缩放大幅减少左边距
//   }

//   @media screen and (max-width: 1400px) {
//     padding: 1.8rem 0px 0px 2rem !important; // 150%缩放进一步减少
//   }
// }

// .bottomRow .panel:nth-child(2) .panelHeader {
//   padding: 2.4rem 0px 0px 3rem !important; // 调整第二个面板的标题位置

//   @media screen and (max-width: 1600px) {
//     padding: 2.4rem 0px 0px 3rem !important; // 125%缩放调整
//   }

//   @media screen and (max-width: 1400px) {
//     padding: 1.4rem 0px 0px 1rem !important; // 150%缩放进一步减少
//   }
// }

.panelTitle {
  font-size: 1.48rem;
  color: #fff;
  padding: 1.5rem 0;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  .title {
    background-image: url('../../assets/IncomingInspectionDashboard/header_title.png');
    background-size: 100% 100%;
    background-position: center;

    height: 4rem;
    width: 4rem;
  }
}

.panelExtra {
  display: flex;
  gap: 1rem;
  margin-top: 0.7rem;
}

.assetButton {
  background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png');
  background-size: 100% 100%;
  background-position: center;
  background-color: transparent;
  border: none;
  color: #c4d1e2;
  padding: 0.3rem 1.5rem;
  border-radius: 0.4rem;
  cursor: pointer;
  font-size: 1.2rem;
}

.filterButton {
  background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png');
  background-size: 100% 100%;
  background-position: center;
  background-color: transparent;
  border: none;
  color: #fff;
  padding: 0.6rem 1.6rem;
  cursor: pointer;
  font-size: 1.4rem;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 6rem;
  height: 3.2rem;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
  }

  &:active {
    transform: scale(0.98);
  }

  &:last-child {
    margin-right: 0;
  }
}

.panelBody {
  flex-grow: 1;
  padding: 0 1.5rem;
  overflow: auto;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  box-sizing: border-box;
  margin-top: 1.2rem;

  @media screen and (max-width: 1600px) {
    padding: 0 0.8rem; // 125%缩放时减少左右内边距
  }

  @media screen and (max-width: 1400px) {
    padding: 0 0.5rem; // 150%缩放时进一步减少左右内边距
  }
}

.chartPanelBody {
  padding: 0;
  position: relative;
  overflow: hidden;
  height: calc(100% - 6rem); // 增加减去的高度，防止溢出
  max-height: calc(45vh - 6rem); // 确保不超过容器最大高度

  > div {
    width: 100% !important;
    height: 100% !important;
    max-height: 100% !important;
  }

  // 响应式调整
  @media screen and (max-width: 1600px) {
    max-height: calc(40vh - 6rem); // 125%缩放时调整
  }

  @media screen and (max-width: 1400px) {
    max-height: calc(35vh - 6rem); // 150%缩放时调整
  }
}

.pieChartBackground {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  background-image: url('../../assets/IncomingInspectionDashboard/pie_bac.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.customTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
  overflow: hidden;
}

.tableHeader,
.tableRow {
  display: grid;
  grid-template-columns: 1.5fr 1.5fr 1.5fr 1.5fr 1.2fr 1fr;
  text-align: center;
  padding: 1rem !important;
  align-items: center;
  gap: 2rem;

  // 针对不同缩放比例的响应式调整
  @media screen and (max-width: 1600px) {
    gap: 0.8rem; // 125%缩放时大幅减少间距防止溢出
    padding: 0.6rem 0.4rem !important;
    grid-template-columns: 1.3fr 1.3fr 1.3fr 1.3fr 1fr 0.8fr; // 调整列宽比例
  }

  @media screen and (max-width: 1400px) {
    gap: 0.5rem; // 150%缩放时进一步减少间距
    padding: 0.4rem 0.2rem !important;
    grid-template-columns: 1.2fr 1.2fr 1.2fr 1.2fr 0.9fr 0.7fr;
  }
}
.tableHeader2,
.tableRow2 {
  display: grid;
  grid-template-columns: 1.5fr 1.5fr 1.5fr 1.5fr;
  text-align: center;
  padding: 1rem !important;
  align-items: center;
  gap: 2rem;

  // 针对不同缩放比例的响应式调整
  @media screen and (max-width: 1600px) {
    gap: 0.8rem; // 125%缩放时大幅减少间距防止溢出
    padding: 0.6rem 0.4rem !important;
    grid-template-columns: 1.5fr 1.5fr 1.5fr 1.5fr; // 保持均匀分布
  }

  @media screen and (max-width: 1400px) {
    gap: 0.5rem; // 150%缩放时进一步减少间距
    padding: 0.4rem 0.2rem !important;
    grid-template-columns: 1.5fr 1.5fr 1.5fr 1.5fr; // 保持均匀分布
  }
}

.tableHeader,
.tableHeader2 {
  background-image: url('../../assets/IncomingInspectionDashboard/table_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  font-weight: bold;
  color: #5a9df4;
  font-size: 1.3rem;
  text-align: start;
  padding-left: 1rem;
  > span {
    white-space: nowrap;
    text-align: start;
    padding: 0 1rem;
  }
}

.tableBody,
.materialFilterModalBody .materialFilterListBody {
  &::-webkit-scrollbar {
    width: 1.2rem !important;
  }
  &::-webkit-scrollbar-track {
    background-color: #0b1a3e !important;
    border: 2px solid #6274a4 !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #869ecd !important;
    border-radius: 0.5rem !important;
    border: 3px solid transparent !important;
    background-clip: content-box !important;
  }
}

.tableBody {
  flex-grow: 1;
  overflow-y: auto;
  color: #ffffff;
  font-size: 1.2em;
  scroll-behavior: smooth;
  max-height: calc(100% - 4rem); // 确保不超出容器高度
}

.tableRow,
.tableRow2 {
  border-bottom: 0.1rem solid #16366b;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
  color: #ffffff;
  font-size: 1.2rem;

  &:nth-child(even) {
    background-color: transparent;
  }

  &:hover {
    background-color: rgba(26, 58, 139, 0.2);
    transform: translateY(-0.1rem);
    box-shadow: 0 0.2rem 0.8rem rgba(74, 144, 226, 0.1);
  }

  &.entering {
    opacity: 0;
    transform: translateY(2rem);
    animation: rowFadeIn 0.5s ease forwards;
  }
}

.clickableLink {
  color: #00ffc5;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;

  &:hover {
    color: #00d4a3;
    text-decoration: underline;
  }
}
.activeRow {
  background-color: rgba(0, 132, 255, 0.3) !important;
}
.tableCell {
  white-space: nowrap;
  overflow: hidden;
  padding: 0 0.5rem;
  position: relative;
  max-width: 100%;
  min-width: 0;
  text-align: start;
  padding-left: 1rem;
}

@keyframes scrollText {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(var(--scroll-amount, -100px));
  }
  100% {
    transform: translateX(0);
  }
}

.scrollingText {
  display: inline-block;
  animation: scrollText var(--scroll-duration, 10s) linear infinite;
  white-space: nowrap;
}

.loadingRow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  color: #b9d4ff;
  font-size: 1.2rem;
  opacity: 0.8;
  background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
  animation: loadingShimmer 2s ease-in-out infinite;

  span {
    animation: loadingPulse 1.5s ease-in-out infinite;
    position: relative;

    &::after {
      content: '...';
      animation: loadingDots 1.5s steps(4, end) infinite;
    }
  }
}

@keyframes rowFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingPulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes loadingShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

@keyframes loadingDots {
  0%,
  20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%,
  100% {
    content: '...';
  }
}
.tableIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('../../assets/IncomingInspectionDashboard/bottom_content_num.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 36px;
  height: 15px;
  margin: 0px 4px;
}

:global(.filter-modal .c7n-pro-modal-content) {
  background: transparent !important;
  box-shadow: none !important;
}

.materialFilterList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.materialFilterControls {
  padding: 20px 10px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  flex-shrink: 0;
}

.filterRow {
  display: flex;
  align-items: center;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 22rem;
}

.filterLabel {
  color: #c3e1e9;
  font-size: 14px;
  white-space: nowrap;
  min-width: 80px;
}

.filterInput {
  flex: 1;
  padding: 6px 12px;
  background-color: rgba(11, 26, 62, 0.8);
  border: 1px solid rgba(74, 144, 226, 0.5);
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  min-width: 120px;

  &::placeholder {
    color: rgba(195, 225, 233, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
}

.queryButton {
  padding: 6px 16px;
  /* background-image: 现在通过JSX style属性设置 */
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  flex-shrink: 0;
  margin-right: 10px;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.clearButton {
  padding: 6px 16px;
  /* background-image: 现在通过JSX style属性设置 */
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  flex-shrink: 0;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.materialFilterListHeader {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 12px 30px;
  background-color: transparent;
  /* background-image: 现在通过JSX style属性设置 */
  font-weight: bold;
  text-align: left;
  color: #c3e1e9;
  font-size: 16px;
  flex-shrink: 0;
}

.materialFilterListBody {
  flex-grow: 1;
  overflow-y: auto;
}

.materialFilterListRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 15px 30px;
  text-align: left;
  cursor: pointer;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
  font-size: 1.2rem;
  color: #ffffff;

  &:nth-child(even) {
    background-color: rgba(0, 132, 255, 0.1);
  }

  &:hover {
    background-color: rgba(74, 144, 226, 0.3);
  }

  &.selected {
    background-color: #4a90e2;
    color: #fff;
  }

  &:last-child {
    border-bottom: none;
  }
}

// 无数据提示样式
.noDataRow {
  padding: 40px 30px;
  text-align: center;
  color: rgba(195, 225, 233, 0.6);
  font-size: 14px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
}

.materialFilterModalFooter {
  padding: 25px 30px 20px;
  text-align: right;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.filterButtonBox {
  display: flex;
  gap: 15px;
}

.modalButton {
  /* background-image: 现在通过JSX style属性设置 */
  border: none;
  color: #fff;
  padding: 10px 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  min-width: 90px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
  }

  &:active {
    transform: scale(0.98);
  }

  &.confirmButton {
    filter: brightness(1.3);
  }

  &.cancelButton {
    filter: brightness(0.8);
  }
}

.modalOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  /* background-image: 现在通过JSX style属性设置 */
}

.materialFilterModalContainer {
  background-color: transparent;
  border: none;
  width: 75rem;
  height: 58rem;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-top: 4.4rem;
}

.materialFilterModalTitle {
  padding: 8px 30px 15px;
  text-align: center;
  flex-shrink: 0;
  position: relative;
  font-size: 22px;
  color: #ffffff;
  z-index: 10;
}

.titleDecorator {
  color: #4a90e2;
  font-size: 16px;
  margin: 0 10px;
}

.materialFilterModalBody {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  height: 38rem;
}

.materialFilterModalFooter {
  padding: 25px 30px 20px;
  text-align: right;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: auto;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 全屏模式样式 - 简化版本，只覆盖必要的属性
:global(#incomingInspectionDashboard:fullscreen),
:global(#incomingInspectionDashboard:-webkit-full-screen),
:global(#incomingInspectionDashboard:-moz-full-screen),
:global(#incomingInspectionDashboard:-ms-fullscreen) {
  width: 100vw !important;
  height: 100vh !important;
  background-color: #0d1224 !important;
  overflow: auto !important;

  .header {
    height: 6rem !important;
    padding: 1rem 2rem !important;
  }

  // 全屏模式下标题保持原有尺寸，覆盖媒体查询
  .title {
    height: 3.6rem !important;
    width: 46rem !important;
  }

  .enTitle {
    font-size: 1.84rem !important;
    letter-spacing: 0.2rem !important;
    padding-top: 0.5rem !important;
  }

  .mainContent {
    margin-top: 5rem !important;
    padding: 0 2rem 2rem !important;
    gap: 2rem !important;

    // 覆盖所有媒体查询的margin-top设置
    @media screen and (max-width: 1600px) {
      margin-top: 5rem !important;
    }

    @media screen and (max-width: 1400px) {
      margin-top: 5rem !important;
    }
  }

  // 全屏模式下所有panelHeader都增加右边距
  .panelHeader {
    padding-right: 1rem !important;
  }

  // 全屏模式下模块标题字号增大一号
  :global(.panelTitle) {
    font-size: 1.68rem !important; // 从1.48rem增大到1.68rem
  }

  // 全屏模式下除了左上角panelHeader外，其他panelHeader增加padding-left
  .topRow > .panel:nth-child(2) .panelHeader {
    padding-left: 4.5rem !important; // 来料检验不良项目统计图，增加更多右移

    // 覆盖媒体查询
    @media screen and (max-width: 1600px) {
      padding-left: 4.5rem !important;
    }

    @media screen and (max-width: 1400px) {
      padding-left: 4.5rem !important;
    }
  }

  .bottomRow > .panel:nth-child(1) .panelHeader {
    padding-left: calc(5rem + 0.4rem) !important;

    // 覆盖媒体查询
    @media screen and (max-width: 1600px) {
      padding-left: calc(5rem + 0.4rem) !important;
    }

    @media screen and (max-width: 1400px) {
      padding-left: calc(2rem + 0.4rem) !important;
    }
  }
}

.fullscreenBottomRow {
  margin-top: 3.5rem !important;
}

@media screen and (min-width: 2560px) {
  .mainContent {
    margin-top: 3.5rem;
    height: calc(100% - 6.5rem);
  }

  .bottomRow {
    margin-left: 1rem;
    overflow: hidden;
    gap: 4rem;
  }
}
