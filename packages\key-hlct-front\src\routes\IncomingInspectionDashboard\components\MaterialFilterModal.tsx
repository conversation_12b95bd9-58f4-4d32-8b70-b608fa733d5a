/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-08-01 18:09:10
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-04 12:25:04
 * @FilePath: \inja-qms-front\packages\key-hlct-front\src\routes\IncomingInspectionDashboard\components\MaterialFilterModal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import styles from '../index.module.less';
import MaterialFilterContent from './MaterialFilterContent';

interface MaterialFilterModalProps {
  isOpen: boolean;
  dataSet: any;
  selectedRecord: any;
  onSelect: (record: any) => void;
  onConfirm: () => void;
  onCancel: () => void;
  materialPageData: any[]; // 添加物料数据参数
  onQuery: (materialCode: string, materialName: string) => void; // 添加查询回调
}

const MaterialFilterModal: React.FC<MaterialFilterModalProps> = ({
  isOpen,
  dataSet,
  selectedRecord,
  onSelect,
  onConfirm,
  onCancel,
  materialPageData,
  onQuery,
}) => {
  if (!isOpen) return null;

  return (
    <div
      className={styles.modalOverlay}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
      }}
    >
      <div className={styles.materialFilterModalContainer}>
        <div className={styles.materialFilterModalTitle}>物料筛选</div>
        <div className={styles.materialFilterModalBody}>
          <MaterialFilterContent
            dataSet={dataSet}
            onSelect={onSelect}
            selectedRecord={selectedRecord}
            materialPageData={materialPageData}
            onQuery={onQuery}
          />
        </div>
        <div className={styles.materialFilterModalFooter}>
          <button
            type="button"
            className={`${styles.filterButton}`}
            onClick={onCancel}
          >
            取消
          </button>
          <button
            type="button"
            className={`${styles.filterButton}`}
            onClick={onConfirm}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  );
};

export default MaterialFilterModal;
